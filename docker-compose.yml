version: '3.8'

services:
  sonarqube:
    image: sonarqube:community
    container_name: sonarqube
    depends_on:
      - db
    ports:
      - "9000:9000"
    environment:
      - SONAR_JDBC_URL=${SONAR_JDBC_URL}
      - SONAR_JDBC_USERNAME=${SONAR_JDBC_USERNAME}
      - SONAR_JDBC_PASSWORD=${SONAR_JDBC_PASSWORD}
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
      - ./.env:/app/.env

  db:
    image: postgres:13
    container_name: sonardb
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgresql:/var/lib/postgresql/data
      - ./.env:/app/.env

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: sql_server
    environment:
      SA_PASSWORD: "${SA_PASSWORD}"
      ACCEPT_EULA: "${ACCEPT_EULA}"
    ports:
      - "1435:1433"
    volumes:
      - sql_data:/var/opt/mssql
      - ./.env:/app/.env
    restart: unless-stopped

volumes:
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
  postgresql:
  sql_data:
