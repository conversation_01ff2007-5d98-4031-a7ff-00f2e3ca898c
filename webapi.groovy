pipeline {
    agent any

    environment {
        SERVER_PROJECT = 'src/Presentation/WebApi/ShinKenShinKunServer/ShinKenShinKunServer.csproj'
        SLACK_CHANNEL = '#ci-notify'
        SLACK_CREDENTIALS_ID = 'slack-token-id'

        SONAR_PROJECT_KEY = 'shinkenshinkun-webapi'
        SONAR_PROJECT_NAME = 'ShinKenShinKun WebApi'
        SONAR_ORG = 'your-org'
    }

    options {
        ansiColor('xterm')
        timestamps()
    }

    stages {
        stage('Slack: Build Start') {
            steps {
                slackSend(channel: "${env.SLACK_CHANNEL}",
                          message: ":rocket: *WebApi Build Started* - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                          tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
            }
        }

        stage('Build & Test') {
            steps {
                bat "msbuild ${env.SERVER_PROJECT} /p:Configuration=Release /p:OutputPath=C:\\inetpub\\wwwroot\\ShinKenShinKunServer"
                bat "vstest.console.exe src\\Tests\\ShinKenShinKunTests\\bin\\Release\\ShinKenShinKunTests.dll"
            }
        }

        stage('SonarQube Analysis') {
            steps {
                withSonarQubeEnv('MySonar') {
                    bat """
                        dotnet-sonarscanner begin /k:\"${env.SONAR_PROJECT_KEY}\" /n:\"${env.SONAR_PROJECT_NAME}\" /o:\"${env.SONAR_ORG}\" /v:\"1.0\" /d:sonar.login=%SONAR_AUTH_TOKEN%
                        msbuild ${env.SERVER_PROJECT} /p:Configuration=Release
                        dotnet-sonarscanner end /d:sonar.login=%SONAR_AUTH_TOKEN%
                    """
                }
            }
        }

        stage('Deploy IIS') {
            steps {
                bat 'iisreset'
            }
        }
    }

    post {
        success {
            slackSend(channel: "${env.SLACK_CHANNEL}",
                      message: ":white_check_mark: WebApi Build & Deploy Success - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                      tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
        }
        failure {
            slackSend(channel: "${env.SLACK_CHANNEL}",
                      message: ":x: WebApi Build or Deploy Failed - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                      tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
        }
    }
}
