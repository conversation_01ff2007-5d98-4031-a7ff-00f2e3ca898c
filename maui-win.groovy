pipeline {
    agent any

    environment {
        MAUI_PROJECT = 'src/Presentation/UI/App/ShinKenShinKun/ShinKenShinKun.csproj'
        UNITTEST_PROJECT = 'src/Tests/ShinKenShinKunTests/ShinKenShinKunTests.csproj'
        SLACK_CHANNEL = '#ci-notify'
        SLACK_CREDENTIALS_ID = 'slack-token-id'

        SONAR_PROJECT_KEY = 'shinkenshinkun-maui'
        SONAR_PROJECT_NAME = 'ShinKenShinKun MAUI'
        SONAR_ORG = 'your-org'
    }

    options {
        ansiColor('xterm')
        timestamps()
    }

    stages {
        stage('Slack: Build Start') {
            steps {
                slackSend(channel: "${env.SLACK_CHANNEL}",
                          message: ":rocket: *MAUI Build Started* - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                          tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
            }
        }

        stage('Restore & Build') {
            steps {
                bat "dotnet restore ${env.MAUI_PROJECT}"
                bat "dotnet build ${env.MAUI_PROJECT} -c Release"
            }
        }

        stage('Run Unit Tests') {
            steps {
                bat "dotnet test ${env.UNITTEST_PROJECT} --no-build --logger trx"
            }
        }

        stage('SonarQube Analysis') {
            steps {
                withSonarQubeEnv('MySonar') {
                    bat """
                        dotnet-sonarscanner begin /k:\"${env.SONAR_PROJECT_KEY}\" /n:\"${env.SONAR_PROJECT_NAME}\" /o:\"${env.SONAR_ORG}\" /v:\"1.0\" /d:sonar.login=%SONAR_AUTH_TOKEN%
                        dotnet build ${env.MAUI_PROJECT} -c Release
                        dotnet-sonarscanner end /d:sonar.login=%SONAR_AUTH_TOKEN%
                    """
                }
            }
        }

        stage('Publish MAUI Windows') {
            steps {
                bat "dotnet publish ${env.MAUI_PROJECT} -c Release -f net9.0-windows10.0.19041.0 -o C:\\Builds\\ShinKenShinKunWin"
            }
        }
    }

    post {
        success {
            slackSend(channel: "${env.SLACK_CHANNEL}",
                      message: ":white_check_mark: MAUI Build Success - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                      tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
        }
        failure {
            slackSend(channel: "${env.SLACK_CHANNEL}",
                      message: ":x: MAUI Build Failed - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                      tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
        }
    }
}
