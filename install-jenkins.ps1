Set-ExecutionPolicy Bypass -Scope Process -Force

function Install-ChocoPackageIfNeeded($packageName) {
    $installed = choco list --local-only | Select-String "^$packageName"
    if ($installed) {
        Write-Host "✔️  $packageName already installed, skipping."
    } else {
        Write-Host "⬇️  Installing $packageName..."
        choco install $packageName -y
    }
}

function Open-FirewallPortIfNeeded($port) {
    $ruleName = "Allow Port $port"
    $ruleExists = Get-NetFirewallRule -DisplayName $ruleName -ErrorAction SilentlyContinue
    if (-not $ruleExists) {
        Write-Host "🔓 Opening firewall port $port..."
        New-NetFirewallRule -DisplayName $ruleName -Direction Inbound -Action Allow -Protocol TCP -LocalPort $port
    } else {
        Write-Host "✔️  Firewall rule for port $port already exists."
    }
}

# Step 1: Install Chocolatey if needed
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "📦 Installing Chocolatey..."
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
} else {
    Write-Host "✔️  Chocolatey already installed."
}

# Step 2: Install Jenkins
Install-ChocoPackageIfNeeded "jenkins"

# Step 3: Ensure Jenkins is installed as Windows Service
$jenkinsService = Get-Service -Name jenkins -ErrorAction SilentlyContinue
if ($jenkinsService) {
    if ($jenkinsService.Status -ne "Running") {
        Write-Host "▶️  Starting Jenkins service..."
        Start-Service jenkins
    } else {
        Write-Host "✔️  Jenkins service already running."
    }
} else {
    Write-Host "⚠️  Jenkins service not found. You may need to restart system or check installation logs."
}

# Step 4: Open port 8080
Open-FirewallPortIfNeeded -port 8080

# Step 5: Show initial admin password
$adminPassPath = "C:\ProgramData\Jenkins\.jenkins\secrets\initialAdminPassword"
if (Test-Path $adminPassPath) {
    $adminPass = Get-Content $adminPassPath
    Write-Host "`n🔑 Initial Jenkins admin password: $adminPass"
} else {
    Write-Host "`n🔐 Jenkins initial admin password file not found yet. Wait until Jenkins starts fully."
}

# Step 6: Offer to open Jenkins in browser
$jenkinsUrl = "http://localhost:8080"
Write-Host "`n🌐 Jenkins URL: $jenkinsUrl"
if ($Host.Name -eq "ConsoleHost") {
    $input = Read-Host "❓ Do you want to open Jenkins in browser now? (y/n)"
    if ($input -eq "y") {
        Start-Process $jenkinsUrl
    }
}

Write-Host "`n✅ Jenkins setup complete."
