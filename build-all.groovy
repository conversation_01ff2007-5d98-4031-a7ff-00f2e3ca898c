pipeline {
    agent any

    environment {
        SOLUTION_PATH = 'C:\\Data\\git\\arcteckenshinkunvup2024\\ShinKenShinKun.sln'
        UNITTEST_PROJECT = 'C:\\Data\\git\\arcteckenshinkunvup2024\\src\\Tests\\ShinKenShinKunTests\\ShinKenShinKunTests.csproj'
        SERVER_PROJECT = 'C:\\Data\\git\\arcteckenshinkunvup2024\\src\\Presentation\\WebApi\\ShinKenShinKunServer\\ShinKenShinKunServer.csproj'
        MAUI_PROJECT = 'C:\\Data\\git\\arcteckenshinkunvup2024\\src\\Presentation\\UI\\App\\ShinKenShinKun\\ShinKenShinKun.csproj'
        SONAR_PROJECT_KEY = 'shinkenshinkun'
        SONAR_PROJECT_NAME = 'ShinKenShinKun'
        SONAR_ORG = 'your-org'                   // Nếu dùng SonarCloud
        SLACK_CHANNEL = '#ci-notify'
        SLACK_CREDENTIALS_ID = 'slack-token-id'
    }

    options {
        ansiColor('xterm')
        timestamps()
    }

    stages {
        stage('Slack: Build Start') {
            steps {
                script {
                    slackSend(channel: "${env.SLACK_CHANNEL}",
                              message: ":rocket: *STARTED* - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                              tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
                }
            }
        }

        stage('Restore & Build') {
            steps {
                bat "dotnet restore \"${env.SOLUTION_PATH}\""
                bat "dotnet build \"${env.SOLUTION_PATH}\" -c Release --no-restore"
            }
        }

        stage('Run Unit Tests') {
            steps {
                bat "dotnet test \"${env.UNITTEST_PROJECT}\" --no-build --logger trx"
            }
        }

        stage('SonarQube Analysis') {
            steps {
                withSonarQubeEnv('MySonar') {
                    bat """
                        dotnet-sonarscanner begin /k:"${env.SONAR_PROJECT_KEY}" /n:"${env.SONAR_PROJECT_NAME}" /v:"1.0" /d:sonar.login=%SONAR_AUTH_TOKEN%
                        dotnet build "${env.SOLUTION_PATH}" -c Release
                        dotnet-sonarscanner end /d:sonar.login=%SONAR_AUTH_TOKEN%
                    """
                }
            }
        }

        stage('Deploy IIS (WebApi)') {
            steps {
                bat """
                    dotnet publish "${env.SERVER_PROJECT}" -c Release -o C:\\inetpub\\wwwroot\\ShinKenShinKunServer
                    iisreset
                """
            }
        }

        stage('Publish MAUI Windows') {
            steps {
                bat """
                    dotnet publish "${env.MAUI_PROJECT}" -c Release -f:net8.0-windows10.0.19041.0 -o C:\\Builds\\ShinKenShinKunWin
                """
            }
        }
    }

    post {
        success {
            slackSend(channel: "${env.SLACK_CHANNEL}",
                      message: ":white_check_mark: *SUCCESS* - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                      tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
        }

        failure {
            slackSend(channel: "${env.SLACK_CHANNEL}",
                      message: ":x: *FAILED* - ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                      tokenCredentialId: "${env.SLACK_CREDENTIALS_ID}")
        }

        always {
            echo "🔁 Pipeline complete"
        }
    }
}
