Set-ExecutionPolicy Bypass -Scope Process -Force

function Install-ChocoPackageIfNeeded($packageName) {
    $installed = choco list --local-only | Select-String "^$packageName"
    if ($installed) {
        Write-Host "✔️  $packageName already installed, skipping."
    } else {
        Write-Host "⬇️  Installing $packageName..."
        choco install $packageName -y
    }
}

function Install-DotNetToolIfNeeded($toolName) {
    $installed = dotnet tool list --global | Select-String "^$toolName"
    if ($installed) {
        Write-Host "✔️  $toolName already installed as .NET tool, skipping."
    } else {
        Write-Host "⬇️  Installing .NET tool: $toolName..."
        dotnet tool install --global $toolName --ignore-failed-sources
    }
}

# Step 1: Install Chocolatey if not available
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "📦 Installing Chocolatey..."
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
} else {
    Write-Host "✔️  Chocolatey already installed."
}

# Step 2: Upgrade Chocolatey
choco upgrade chocolatey -y

# Step 3: Install required packages if not already installed
Install-ChocoPackageIfNeeded "dotnet-9.0-sdk"
Install-ChocoPackageIfNeeded "netfx-4.8-devpack"
Install-ChocoPackageIfNeeded "visualstudio2022community"

# Step 4: Install SonarScanner .NET tool if needed
Install-DotNetToolIfNeeded "dotnet-sonarscanner"

# Step 5: Add dotnet tools path to machine PATH if not exists
$envPath = [System.Environment]::GetEnvironmentVariable("PATH", [System.EnvironmentVariableTarget]::Machine)
$dotnetToolsPath = "$env:USERPROFILE\.dotnet\tools"
if ($envPath -notlike "*$dotnetToolsPath*") {
    Write-Host "➕ Adding .NET tools path to system PATH"
    [System.Environment]::SetEnvironmentVariable("PATH", "$envPath;$dotnetToolsPath", [System.EnvironmentVariableTarget]::Machine)
} else {
    Write-Host "✔️  .NET tools path already in PATH"
}

# Step 6: Visual Studio workloads check
Write-Host "`n🛠  Ensure the following Visual Studio workloads are installed manually (or reinstall with params):"
Write-Host " - .NET Desktop Development"
Write-Host " - .NET MAUI"
Write-Host " - ASP.NET and Web Development"
Write-Host "`nYou can reinstall with:"
Write-Host "choco install visualstudio2022community --package-parameters `"
Write-Host "`\"--add Microsoft.VisualStudio.Workload.ManagedDesktop --add Microsoft.VisualStudio.Workload.NetCrossPlat --add Microsoft.VisualStudio.Workload.NetWeb --includeRecommended --passive`\" -y"

Write-Host "`n🎉 Setup complete! Restart your system to apply environment changes."
